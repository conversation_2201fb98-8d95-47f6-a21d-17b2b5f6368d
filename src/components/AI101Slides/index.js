import React from 'react';
import Translate from '@docusaurus/Translate';
import Heading from '@theme/Heading';
import styles from '../../pages/ai101.module.css';

// AI Communication slide
export function AICommunicationSlide() {
  return (
    <section className={styles.slide} id="slide-18">
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide18.title">
            AI Communication Skills
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide18.subtitle">
            Making AI Your Capable Assistant
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>💬</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide18.clearPrompting.title">
                Clear Prompting
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide18.clearPrompting.content">
                Be specific, provide context, and set clear expectations
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🔄</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide18.iterativeRefinement.title">
                Iterative Refinement
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide18.iterativeRefinement.content">
                Refine prompts based on AI responses for better results
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🎭</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide18.rolePlaying.title">
                Role Playing
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide18.rolePlaying.content">
                Ask AI to take on specific roles or perspectives
              </Translate>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            <Translate id="ai101.slide18.patterns.title">
              Effective Prompt Patterns
            </Translate>
          </h3>
          <div className={styles.codeBlock}>
            <Translate id="ai101.slide18.patterns.content">
              {`1. Context + Task + Format
"As an education expert, create a lesson plan for [topic] in [format]"

2. Few-shot Examples
"Here are 3 examples of good questions: [examples]. Now create 5 similar questions about [topic]"

3. Chain of Thought
"Let's think step by step to solve this problem..."`}
            </Translate>
          </div>
        </div>
      </div>
    </section>
  );
}

// Generative Era Learning slide
export function GenerativeEraLearningSlide() {
  return (
    <section className={styles.slide} id="slide-19">
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide19.title">
            Learning and Creation in the Generative Era
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide19.subtitle">
            From Scarcity to Abundance
          </Translate>
        </p>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            <Translate id="ai101.slide19.paradigm.title">
              Paradigm Shift
            </Translate>
          </h3>
          <div className={styles.comparisonTable}>
            <table>
              <thead>
                <tr>
                  <th>
                    <Translate id="ai101.slide19.paradigm.traditional">
                      Traditional Era
                    </Translate>
                  </th>
                  <th>
                    <Translate id="ai101.slide19.paradigm.generative">
                      Generative Era
                    </Translate>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>
                    <Translate id="ai101.slide19.paradigm.traditional.item1">
                      Information Scarcity
                    </Translate>
                  </td>
                  <td>
                    <Translate id="ai101.slide19.paradigm.generative.item1">
                      Information Abundance
                    </Translate>
                  </td>
                </tr>
                <tr>
                  <td>
                    <Translate id="ai101.slide19.paradigm.traditional.item2">
                      Content Creation is Hard
                    </Translate>
                  </td>
                  <td>
                    <Translate id="ai101.slide19.paradigm.generative.item2">
                      Content Creation is Easy
                    </Translate>
                  </td>
                </tr>
                <tr>
                  <td>
                    <Translate id="ai101.slide19.paradigm.traditional.item3">
                      Focus on Memorization
                    </Translate>
                  </td>
                  <td>
                    <Translate id="ai101.slide19.paradigm.generative.item3">
                      Focus on Critical Evaluation
                    </Translate>
                  </td>
                </tr>
                <tr>
                  <td>
                    <Translate id="ai101.slide19.paradigm.traditional.item4">
                      Individual Learning
                    </Translate>
                  </td>
                  <td>
                    <Translate id="ai101.slide19.paradigm.generative.item4">
                      AI-Assisted Learning
                    </Translate>
                  </td>
                </tr>
                <tr>
                  <td>
                    <Translate id="ai101.slide19.paradigm.traditional.item5">
                      Linear Curriculum
                    </Translate>
                  </td>
                  <td>
                    <Translate id="ai101.slide19.paradigm.generative.item5">
                      Personalized Pathways
                    </Translate>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            <Translate id="ai101.slide19.priorities.title">
              New Learning Priorities
            </Translate>
          </h3>
          <ul className={styles.bulletList}>
            <li>
              <Translate id="ai101.slide19.priorities.item1">
                Develop information literacy and source evaluation skills
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide19.priorities.item2">
                Learn to collaborate effectively with AI tools
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide19.priorities.item3">
                Focus on creativity, critical thinking, and problem-solving
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide19.priorities.item4">
                Cultivate emotional intelligence and human connection
              </Translate>
            </li>
          </ul>
        </div>
      </div>
    </section>
  );
}

// Lifelong Learning slide
export function LifelongLearningSlide() {
  return (
    <section className={styles.slide} id="slide-20">
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide20.title">
            New Paradigm of Lifelong Learning
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide20.subtitle">
            From Staged Education to Continuous Growth
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🔄</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide20.continuous.title">
                Continuous Learning
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide20.continuous.content">
                Learning becomes a continuous process throughout life
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🎯</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide20.justInTime.title">
                Just-in-Time Learning
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide20.justInTime.content">
                Learn what you need, when you need it, with AI assistance
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>👥</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide20.collaborative.title">
                Collaborative Learning
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide20.collaborative.content">
                Learn with and from both humans and AI systems
              </Translate>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            <Translate id="ai101.slide20.infrastructure.title">
              Building Learning Infrastructure
            </Translate>
          </h3>
          <div className={styles.codeBlock}>
            <Translate id="ai101.slide20.infrastructure.content">
              {`Traditional: School → University → Work
                    ↓
AI Era: Continuous Learning Ecosystem
        ↓
School + AI → University + AI → Work + AI → Retirement + AI`}
            </Translate>
          </div>
        </div>
      </div>
    </section>
  );
}

// AI Literacy slide
export function AILiteracySlide() {
  return (
    <section className={styles.slide} id="slide-21">
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide21.title">
            Cultivating AI Literacy
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide21.subtitle">
            Technology Should Amplify Human Potential, Not Replace Humans
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>💡</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide21.understanding.title">
                Understanding AI
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide21.understanding.content">
                Know how AI works, its capabilities and limitations
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>⚖️</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide21.ethics.title">
                Ethical Awareness
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide21.ethics.content">
                Understand bias, privacy, and ethical implications of AI
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🛠️</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide21.skills.title">
                Practical Skills
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide21.skills.content">
                Learn to use AI tools effectively and responsibly
              </Translate>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            <Translate id="ai101.slide21.curriculum.title">
              AI Literacy Curriculum
            </Translate>
          </h3>
          <ul className={styles.bulletList}>
            <li>
              <Translate id="ai101.slide21.curriculum.item1">
                Basic AI concepts and terminology
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide21.curriculum.item2">
                Hands-on experience with AI tools
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide21.curriculum.item3">
                Critical evaluation of AI-generated content
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide21.curriculum.item4">
                Ethics and responsible AI use
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide21.curriculum.item5">
                Future implications and career preparation
              </Translate>
            </li>
          </ul>
        </div>
      </div>
    </section>
  );
}

// Educational AI Products slide
export function EducationalAISlide() {
  return (
    <section className={styles.slide} id="slide-22">
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide22.title">
            Education-Related AI Products
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide22.subtitle">
            Making AI Your Educational Assistant
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>💬</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide22.conversational.title">
                Conversational AI
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide22.conversational.content">
                ChatGPT, Claude, Gemini for Q&A and tutoring
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>📝</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide22.writing.title">
                Writing Assistants
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide22.writing.content">
                Grammarly, Notion AI for content creation and editing
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🎨</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide22.creative.title">
                Creative Tools
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide22.creative.content">
                DALL-E, Midjourney for visual content creation
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🔍</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide22.data.title">
                Research Tools
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide22.data.content">
                Information retrieval, summarization, analysis tools
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🎓</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide22.learning.title">
                Learning Platforms
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide22.learning.content">
                Khan Academy AI, Coursera AI for personalized learning
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>💡</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide22.thinking.title">
                Thinking Tools
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide22.thinking.content">
                FunBlocks AI for mindmapping and structured thinking
              </Translate>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// FunBlocks AI slide
export function FunBlocksAISlide() {
  return (
    <section className={styles.slide} id="slide-23">
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide23.title">
            FunBlocks AI
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide23.subtitle">
            Explore, Think and Create with AI
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🔍</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide23.mindMapping.title">
                Critical Thinking
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide23.mindMapping.content">
                Refine questions, analyze fallacies, and develop critical thinking skills
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>💡</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide23.brainstorming.title">
                Creative Thinking
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide23.brainstorming.content">
                Generate and develop creative ideas with AI
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>⭐</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide23.exploration.title">
                Boundless Exploration
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide23.exploration.content">
                Explore endless possibilities with AI on boundless canvas from multiple perspectives
              </Translate>
            </div>
          </div>
          

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>⭐</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide23.structuredThinking.title">
                Structured Thinking
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide23.structuredThinking.content">
                AI-powered mind mapping and brainstorming
              </Translate>
            </div>
          </div>
          
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🤖</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide23.mentalModels.title">
                AI-Driven Mental Models
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide23.mentalModels.content">
                Apply classic mental models to complex problems with AI assistance
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🎨</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide23.creativeWorkflows.title">
                Creative Workflows
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide23.creativeWorkflows.content">
                From concept to presentation with integrated AI tools
              </Translate>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            <Translate id="ai101.slide23.features.title">
              Key Features
            </Translate>
          </h3>
          <ul className={styles.bulletList}>
            <li>
              <Translate id="ai101.slide23.features.item1">
                AI-powered mind mapping and brainstorming
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide23.features.item2">
                AI Collaborative thinking spaces
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide23.features.item3">
                Integration with multiple AI models
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide23.features.item4">
                Export to various formats (slides, documents, etc.)
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide23.features.item5">
                Educational templates and frameworks
              </Translate>
            </li>
          </ul>
        </div>
      </div>
    </section>
  );
}