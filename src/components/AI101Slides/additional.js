import React from 'react';
import Translate from '@docusaurus/Translate';
import Heading from '@theme/Heading';
import styles from '../../pages/ai101.module.css';

// Innovation with AI slide
export function InnovationWithAISlide() {
  return (
    <section className={styles.slide} id="slide-24">
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide24.title">
            Why Use AI to Help Innovation and Enhance Thinking?
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide24.subtitle">
            Breaking Through Human Cognitive Limitations
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🧠</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide24.cognitiveAugmentation.title">
                Cognitive Augmentation
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide24.cognitiveAugmentation.content">
                AI extends human thinking capacity and processing power
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🔍</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide24.patternRecognition.title">
                Pattern Recognition
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide24.patternRecognition.content">
                Discover hidden connections and insights in complex data
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>⚡</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide24.speedScale.title">
                Speed & Scale
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide24.speedScale.content">
                Process information and generate ideas at unprecedented speed
              </Translate>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            <Translate id="ai101.slide24.limitations.title">
              Human Cognitive Limitations
            </Translate>
          </h3>
          <ul className={styles.bulletList}>
            <li>
              <Translate id="ai101.slide24.limitations.item1">
                Working memory constraints (7±2 items)
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide24.limitations.item2">
                Confirmation bias and cognitive biases
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide24.limitations.item3">
                Limited processing speed for complex information
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide24.limitations.item4">
                Difficulty in seeing patterns across large datasets
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide24.limitations.item5">
                Fatigue and attention limitations
              </Translate>
            </li>
            <li>
              ...
            </li>
          </ul>

          <h3 className={styles.cardTitle}>
            <Translate id="ai101.slide24.partner.title">
              AI as Cognitive Partner
            </Translate>
          </h3>
          <div className={styles.codeBlock}>
            <Translate id="ai101.slide24.partner.content">
              {`Human Creativity + AI Processing Power = Enhanced Innovation
            
Human: Intuition, Context, Values
AI: Speed, Scale, Pattern Recognition
Together: Breakthrough Solutions`}
            </Translate>
          </div>
        </div>
      </div>
    </section>
  );
}

// Linear Thinking Limitations slide
export function LinearThinkingSlide() {
  return (
    <section className={styles.slide} id="slide-25">
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide25.title">
            Breaking Through Linear Thinking Limitations
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide25.subtitle">
            From Chat Box to Thinking Network
          </Translate>
        </p>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            <Translate id="ai101.slide25.comparison.title">
              Traditional vs. Network Thinking
            </Translate>
          </h3>
          <div className={styles.comparisonTable}>
            <table>
              <thead>
                <tr>
                  <th>
                    <Translate id="ai101.slide25.comparison.linear">
                      Linear Thinking
                    </Translate>
                  </th>
                  <th>
                    <Translate id="ai101.slide25.comparison.network">
                      Network Thinking
                    </Translate>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>
                    <Translate id="ai101.slide25.comparison.linear.item1">
                      Sequential, step-by-step
                    </Translate>
                  </td>
                  <td>
                    <Translate id="ai101.slide25.comparison.network.item1">
                      Multi-dimensional, interconnected
                    </Translate>
                  </td>
                </tr>
                <tr>
                  <td>
                    <Translate id="ai101.slide25.comparison.linear.item2">
                      Single conversation thread
                    </Translate>
                  </td>
                  <td>
                    <Translate id="ai101.slide25.comparison.network.item2">
                      Multiple parallel explorations
                    </Translate>
                  </td>
                </tr>
                <tr>
                  <td>
                    <Translate id="ai101.slide25.comparison.linear.item3">
                      Limited context retention
                    </Translate>
                  </td>
                  <td>
                    <Translate id="ai101.slide25.comparison.network.item3">
                      Rich contextual relationships
                    </Translate>
                  </td>
                </tr>
                <tr>
                  <td>
                    <Translate id="ai101.slide25.comparison.linear.item4">
                      Isolated problem solving
                    </Translate>
                  </td>
                  <td>
                    <Translate id="ai101.slide25.comparison.network.item4">
                      Holistic system thinking
                    </Translate>
                  </td>
                </tr>
                <tr>
                  <td>
                    <Translate id="ai101.slide25.comparison.linear.item5">
                      Focus on result
                    </Translate>
                  </td>
                  <td>
                    <Translate id="ai101.slide25.comparison.network.item5">
                      Focus on process
                    </Translate>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            <Translate id="ai101.slide25.benefits.title">
              Network Thinking Benefits
            </Translate>
          </h3>
          <ul className={styles.bulletList}>
            <li>
              <Translate id="ai101.slide25.benefits.item1">
                Explore multiple perspectives simultaneously
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide25.benefits.item2">
                Maintain context across different idea branches
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide25.benefits.item3">
                Visualize relationships between concepts
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide25.benefits.item4">
                Enable non-linear creative processes
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide25.benefits.item5">
                Support complex problem decomposition, divide and conquer
              </Translate>
            </li>
          </ul>
        </div>
      </div>
    </section>
  );
}

// AI Enhanced Thinking slide
export function AIEnhancedThinkingSlide() {
  return (
    <section className={styles.slide} id="slide-26">
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide26.title">
            Using AI to Enhance Thinking Ability
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide26.subtitle">
            Let AI Assist Thinking, Not Replace Thinking
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🤝</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide26.partner.title">
                AI as Partner
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide26.partner.content">
                AI augments human intelligence rather than replacing it
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🔍</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide26.analysis.title">
                Enhanced Analysis
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide26.analysis.content">
                Process complex information and identify patterns faster
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>💡</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide26.creative.title">
                Creative Catalyst
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide26.creative.content">
                Spark new ideas and explore alternative perspectives
              </Translate>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            <Translate id="ai101.slide26.strategies.title">
              Thinking Enhancement Strategies
            </Translate>
          </h3>
          <ul className={styles.bulletList}>
            <li>
              <Translate id="ai101.slide26.strategies.item1">
                Use AI for rapid information gathering and synthesis
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide26.strategies.item2">
                Leverage AI to challenge assumptions and biases
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide26.strategies.item3">
                Employ AI for scenario planning and what-if analysis
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide26.strategies.item4">
                Utilize AI to structure and organize complex thoughts
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide26.strategies.item5">
                Apply AI for cross-domain knowledge connections
              </Translate>
            </li>
          </ul>

          <div className={styles.codeBlock}>
            <Translate id="ai101.slide26.process.content">
              {`Human Critical Thinking + AI Processing = Enhanced Decision Making
            
1. Human sets goals and context
2. AI provides data and analysis
3. Human applies judgment and values
4. AI helps explore implications
5. Human makes final decisions`}
            </Translate>
          </div>
        </div>
      </div>
    </section>
  );
}

// Summary and Outlook slide
export function SummaryOutlookSlide() {
  return (
    <section className={styles.slide} id="slide-27">
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide27.title">
            Summary and Outlook
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide27.subtitle">
            Embracing Educational Transformation in the AI Era
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🎓</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide27.transformation.title">
                Educational Transformation
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide27.transformation.content">
                AI will fundamentally reshape how we teach and learn
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🤝</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide27.partnership.title">
                Human-AI Partnership
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide27.partnership.content">
                The future belongs to those who can collaborate with AI
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🌟</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide27.adaptation.title">
                Continuous Adaptation
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide27.adaptation.content">
                Embrace change and maintain a growth mindset
              </Translate>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            <Translate id="ai101.slide27.takeaways.title">
              Key Takeaways
            </Translate>
          </h3>
          <ul className={styles.bulletList}>
            <li>
              <Translate id="ai101.slide27.takeaways.item1">
                AI is a powerful tool for enhancing human capabilities
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide27.takeaways.item2">
                Focus on developing uniquely human skills
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide27.takeaways.item3">
                Learn to collaborate effectively with AI systems
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide27.takeaways.item4">
                Cultivate AI literacy and ethical awareness
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide27.takeaways.item5">
                Embrace lifelong learning and adaptation
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide27.takeaways.item6">
                Use AI to amplify creativity and critical thinking
              </Translate>
            </li>
          </ul>
        </div>
      </div>
    </section>
  );
}

// Thank You slide
export function ThankYouSlide() {
  return (
    <section className={styles.slide} id="slide-28">
      <div className={styles.slideContent}>
        <Heading as="h1" className={styles.slideTitle}>
          <Translate id="ai101.slide28.title">
            AI已来，打不过就加入!
          </Translate>
        </Heading>

        <div className={styles.decorativeShape + ' ' + styles.circle} style={{ top: '20%', left: '15%' }}></div>
        <div className={styles.decorativeShape + ' ' + styles.triangle} style={{ bottom: '20%', right: '15%' }}></div>
      </div>
    </section>
  );
}

// New slide about LLM Hallucination
export function LLMHallucinationSlide() {
  return (
    <section className={styles.slide} id="slide-hallucination">
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.hallucination.title">
            LLM Hallucination
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.hallucination.subtitle">
            What is Hallucination?
          </Translate>
        </p>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            <Translate id="ai101.hallucination.definition.title">
              Definition
            </Translate>
          </h3>
          <div className={styles.cardContent}>
            <Translate id="ai101.hallucination.definition.content">
              Models generate seemingly plausible but actually inaccurate or non-existent information
            </Translate>
          </div>

          <h3 className={styles.cardTitle}>
            <Translate id="ai101.hallucination.types.title">
              Main Types of Hallucination
            </Translate>
          </h3>
          <div className={styles.contentGrid}>
            <div className={styles.contentCard}>
              <span className={styles.cardIcon}>🔍</span>
              <h4 className={styles.cardTitle}>
                <Translate id="ai101.hallucination.types.factual.title">
                  Factual Hallucination
                </Translate>
              </h4>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.hallucination.types.factual.item1">
                    False information: Generating non-existent historical events, people, or data
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.types.factual.item2">
                    Fake citations: Fabricating non-existent academic papers, website links
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.types.factual.item3">
                    Numerical errors: Providing incorrect statistics, dates, quantities
                  </Translate>
                </li>
              </ul>
            </div>

            <div className={styles.contentCard}>
              <span className={styles.cardIcon}>🧠</span>
              <h4 className={styles.cardTitle}>
                <Translate id="ai101.hallucination.types.logical.title">
                  Logical Hallucination
                </Translate>
              </h4>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.hallucination.types.logical.item1">
                    Reasoning errors: Fallacies in logical deduction
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.types.logical.item2">
                    Causal confusion: Incorrectly establishing causal relationships
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.types.logical.item3">
                    Self-contradiction: Contradictory statements within the same response
                  </Translate>
                </li>
              </ul>
            </div>

            <div className={styles.contentCard}>
              <span className={styles.cardIcon}>🎭</span>
              <h4 className={styles.cardTitle}>
                <Translate id="ai101.hallucination.types.creative.title">
                  Creative Hallucination
                </Translate>
              </h4>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.hallucination.types.creative.item1">
                    Fictional content: Creating non-existent stories, characters, works
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.types.creative.item2">
                    Mixed information: Incorrectly combining information from different sources
                  </Translate>
                </li>
              </ul>
            </div>
          </div>

          <h3 className={styles.cardTitle}>
            <Translate id="ai101.hallucination.causes.title">
              Causes
            </Translate>
          </h3>
          <div className={styles.contentGrid}>
            <div className={styles.contentCard}>
              <h4 className={styles.cardTitle}>
                <Translate id="ai101.hallucination.causes.training.title">
                  Training Data Issues
                </Translate>
              </h4>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.hallucination.causes.training.item1">
                    Errors in training data
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.causes.training.item2">
                    Incomplete training coverage
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.causes.training.item3">
                    Outdated or contradictory information
                  </Translate>
                </li>
              </ul>
            </div>

            <div className={styles.contentCard}>
              <h4 className={styles.cardTitle}>
                <Translate id="ai101.hallucination.causes.model.title">
                  Model Mechanism Limitations
                </Translate>
              </h4>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.hallucination.causes.model.item1">
                    Probability-based generation
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.causes.model.item2">
                    Lack of real-world knowledge verification
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.causes.model.item3">
                    Context understanding limitations
                  </Translate>
                </li>
              </ul>
            </div>
          </div>

          <h3 className={styles.cardTitle}>
            <Translate id="ai101.hallucination.strategies.title">
              Identification and Prevention Strategies
            </Translate>
          </h3>
          <div className={styles.contentGrid}>
            <div className={styles.contentCard}>
              <h4 className={styles.cardTitle}>
                <Translate id="ai101.hallucination.strategies.user.title">
                  User Level
                </Translate>
              </h4>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.hallucination.strategies.user.item1">
                    Cross-verification: Verify important information from multiple sources
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.strategies.user.item2">
                    Critical thinking: Maintain skepticism, especially for specific data
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.strategies.user.item3">
                    Professional judgment: Rely on authoritative resources in professional fields
                  </Translate>
                </li>
              </ul>
            </div>

            <div className={styles.contentCard}>
              <h4 className={styles.cardTitle}>
                <Translate id="ai101.hallucination.strategies.technical.title">
                  Technical Level
                </Translate>
              </h4>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.hallucination.strategies.technical.item1">
                    Retrieval Augmented Generation (RAG): Combine with real-time knowledge base
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.strategies.technical.item2">
                    Multi-model verification: Cross-verify using multiple models
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.strategies.technical.item3">
                    Confidence assessment: Label answer reliability
                  </Translate>
                </li>
              </ul>
            </div>
          </div>

          <h3 className={styles.cardTitle}>
            <Translate id="ai101.hallucination.keyPoints.title">
              Key Points
            </Translate>
          </h3>
          <blockquote className={styles.quote}>
            <Translate id="ai101.hallucination.keyPoints.content">
              🚨 Remember: Large language models are powerful tools, but require human judgment and verification to ensure information accuracy
            </Translate>
          </blockquote>
        </div>
      </div>
    </section>
  );
}
